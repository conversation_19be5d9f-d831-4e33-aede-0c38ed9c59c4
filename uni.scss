/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

@import '@/uni_modules/uv-ui-tools/theme.scss';

// ===== 设计令牌系统 (Design Tokens) =====

/* 主色调系统 - 统一品牌色彩 */
$primary-color: #3c9cff;           // 主品牌色 (使用uv-ui标准色)
$primary-color-light: #ecf5ff;     // 主色浅色
$primary-color-dark: #398ade;      // 主色深色
$primary-gradient: linear-gradient(135deg, #3c9cff 0%, #398ade 100%); // 主色渐变

/* 功能色系统 */
$success-color: #5ac725;           // 成功色
$success-light: #f5fff0;           // 成功色浅色
$warning-color: #f9ae3d;           // 警告色
$warning-light: #fdf6ec;           // 警告色浅色
$error-color: #f56c6c;             // 错误色
$error-light: #fef0f0;             // 错误色浅色
$info-color: #909399;              // 信息色
$info-light: #f4f4f5;             // 信息色浅色

/* 中性色系统 */
$text-color-primary: #303133;      // 主要文字
$text-color-regular: #606266;      // 常规文字
$text-color-secondary: #909193;    // 次要文字
$text-color-placeholder: #c0c4cc;  // 占位文字
$text-color-disabled: #c8c9cc;     // 禁用文字

/* 背景色系统 */
$bg-color-page: #f8f9fa;           // 页面背景
$bg-color-container: #ffffff;      // 容器背景
$bg-color-overlay: #fafafa;        // 覆盖层背景
$bg-color-hover: #f1f1f1;          // 悬停背景
$bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩背景

/* 边框色系统 */
$border-color-base: #dadbde;       // 基础边框
$border-color-light: #f0f0f0;      // 浅色边框
$border-color-lighter: #fafafa;    // 更浅边框

/* 字体大小系统 (rpx单位，适配小程序) */
$font-size-xs: 22rpx;              // 极小字体
$font-size-sm: 24rpx;              // 小字体
$font-size-base: 28rpx;            // 基础字体
$font-size-lg: 32rpx;              // 大字体
$font-size-xl: 36rpx;              // 超大字体
$font-size-xxl: 42rpx;             // 标题字体
$font-size-xxxl: 48rpx;            // 大标题字体

/* 字重系统 */
$font-weight-light: 300;           // 细体
$font-weight-normal: 400;          // 常规
$font-weight-medium: 500;          // 中等
$font-weight-semibold: 600;        // 半粗体
$font-weight-bold: 700;            // 粗体

/* 间距系统 (rpx单位) */
$spacing-xs: 8rpx;                 // 极小间距
$spacing-sm: 16rpx;                // 小间距
$spacing-base: 24rpx;              // 基础间距
$spacing-lg: 32rpx;                // 大间距
$spacing-xl: 48rpx;                // 超大间距

/* 圆角系统 */
$border-radius-xs: 4rpx;           // 极小圆角
$border-radius-sm: 8rpx;           // 小圆角
$border-radius-base: 12rpx;        // 基础圆角
$border-radius-lg: 16rpx;          // 大圆角
$border-radius-xl: 24rpx;          // 超大圆角
$border-radius-pill: 100px;        // 胶囊圆角

/* 阴影系统 */
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);      // 小阴影
$shadow-base: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);   // 基础阴影
$shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);      // 大阴影

/* 动画时长 */
$transition-base: 0.3s;            // 基础动画时长
$transition-fast: 0.2s;            // 快速动画

// ===== 兼容原有变量 =====
/* 保持向后兼容 */
$uni-color-primary: $primary-color;
$uni-color-success: $success-color;
$uni-color-warning: $warning-color;
$uni-color-error: $error-color;

$uni-text-color: $text-color-primary;
$uni-text-color-inverse: #fff;
$uni-text-color-grey: $text-color-secondary;
$uni-text-color-placeholder: $text-color-placeholder;
$uni-text-color-disable: $text-color-disabled;

$uni-bg-color: $bg-color-container;
$uni-bg-color-grey: $bg-color-page;
$uni-bg-color-hover: $bg-color-hover;
$uni-bg-color-mask: $bg-color-mask;

$uni-border-color: $border-color-base;

/* 尺寸变量 (保持px单位兼容) */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;

$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

$uni-opacity-disabled: 0.3;

/* 文章场景相关 */
$uni-color-title: $text-color-primary;
$uni-font-size-title: 20px;
$uni-color-subtitle: $text-color-regular;
$uni-font-size-subtitle: 26px;
$uni-color-paragraph: $text-color-regular;
$uni-font-size-paragraph: 15px;

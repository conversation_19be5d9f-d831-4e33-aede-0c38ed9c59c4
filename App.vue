<script>
export default {
  onLaunch: function () {
    uni.hideTabBar()
    // console.log('App Launch')
  },
  onShow: function () {
    // console.log('App Show')
  },
  onHide: function () {
    // console.log('App Hide')
  }
}
</script>
<style lang="scss">
/*每个页面公共css */
@import '@/uni_modules/uv-ui-tools/index.scss';

page {
  height: calc(100vh - 88rpx);
  width: 100%;
  background: $bg-color-page;
  color: $text-color-primary;
  font-size: $font-size-base;
  line-height: 1.5;
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

/* 通用工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-base { margin-bottom: $spacing-base; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-base { margin-top: $spacing-base; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-base { padding: $spacing-base; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }
</style>
